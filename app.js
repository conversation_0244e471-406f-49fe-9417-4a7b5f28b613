document.addEventListener('DOMContentLoaded', () => {
    // --- GLOBAL STATE AND CONFIGURATION ---
    let map;
    let currentUser = null;
    let userRole = null;
    let polesData = [];
    let turbinesData = [];    let mapMarkers = [];
    let turbineMarkers = [];    // User database (in a real app, this would be on a server)
    const users = {
        'BrandSA': {
            password: 'RedRocket',
            name: 'BrandSA Client',
            role: 'client',
            company: 'BrandSA'
        },
        'RedRocket': {
            password: 'RedRocket',
            name: 'Red Rocket Client',
            role: 'client',
            company: 'Red Rocket'
        },
        'ADS': {
            password: 'RedRocket',
            name: 'ADS Admin',
            role: 'admin',
            company: 'Aerial Dynamic Solutions'
        }
    };

    // --- DOM ELEMENT REFERENCES ---
    const loginPage = document.getElementById('loginPage');
    const mainApp = document.getElementById('mainApp');
    const loginForm = document.getElementById('loginForm');
    const logoutBtn = document.getElementById('logoutBtn');
    const poleNumberInput = document.getElementById('poleNumber');
    const latitudeInput = document.getElementById('latitude');
    const longitudeInput = document.getElementById('longitude');
    const pdfReportInput = document.getElementById('pdfReport');
    const thumbnailImageInput = document.getElementById('thumbnailImage');
    const addPoleButton = document.getElementById('addPoleButton');
    const turbineNameInput = document.getElementById('turbineName');
    const turbineLatitudeInput = document.getElementById('turbineLatitude');
    const turbineLongitudeInput = document.getElementById('turbineLongitude');
    const turbinePdfReportInput = document.getElementById('turbinePdfReport');
    const turbineThumbnailImageInput = document.getElementById('turbineThumbnailImage');
    const addTurbineButton = document.getElementById('addTurbineButton');
    const saveDataButton = document.getElementById('saveDataButton');
    const loadDataButton = document.getElementById('loadDataButton');
    const loadFileInput = document.getElementById('loadFileInput');
    const refreshDataButton = document.getElementById('refreshDataButton');
    const poleTabBtn = document.getElementById('poleTabBtn');
    const turbineTabBtn = document.getElementById('turbineTabBtn');    const pdfModal = document.getElementById('pdfModal');
    const pdfModalIframe = document.getElementById('pdfModalIframe');
    const modalCloseButton = document.querySelector('.modal-close-button');
    
    // Search elements
    const assetSearchInput = document.getElementById('assetSearchInput');
    const searchButton = document.getElementById('searchButton');
    const searchResults = document.getElementById('searchResults');

    // --- NOTIFICATION SYSTEM ---
    function showNotification(message, type = 'info') {
        const container = document.getElementById('notification-area');
        if (!container) {
            console.warn('Notification area not found. Falling back to alert.');
            alert(message);
            return;
        }
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        const icon = type === 'success' ? 'fas fa-check-circle' :
                     type === 'error' ? 'fas fa-exclamation-circle' :
                     type === 'warning' ? 'fas fa-exclamation-triangle' :
                     'fas fa-info-circle';
        notification.innerHTML = `<i class="${icon}"></i><span>${message}</span>`;
        container.appendChild(notification);
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);    }

    // --- USER AUTHENTICATION & UI MANAGEMENT ---
    async function loginUser(username, role, name) {
        currentUser = username;
        userRole = role;

        loginPage.style.display = 'none';
        mainApp.style.display = 'flex';
        logoutBtn.style.display = 'block';
        updateUIForRole(role);
        showNotification(`Welcome back, ${name}!`, 'success');
        
        // Ensure map is initialized before loading data to prevent race conditions
        if (!map) {
            initMap(); // Synchronous map setup
        }
        await loadDataFromServer(); // Asynchronously load data
    }

    function logout() {
        localStorage.removeItem('currentUser');
        currentUser = null;
        userRole = null;
        loginPage.style.display = 'flex';
        mainApp.style.display = 'none';
        logoutBtn.style.display = 'none';
        showNotification('Logged out successfully', 'info');
    }

    function updateUIForRole(role) {
        const isAdmin = role === 'admin';
        
        // Hide/show admin-only elements
        document.getElementById('adminControls').style.display = isAdmin ? 'block' : 'none';
        document.getElementById('poleForm').style.display = isAdmin ? 'block' : 'none';
        document.getElementById('turbineForm').style.display = isAdmin ? 'block' : 'none';
        
        // Hide/show asset type selector for adding new assets
        const assetTypeSelector = document.querySelector('.asset-type-selector');
        if (assetTypeSelector) {
            assetTypeSelector.style.display = isAdmin ? 'block' : 'none';
        }
        
        // Hide Import Data button for clients, keep Export Data and Refresh Data for all users
        const loadDataButton = document.getElementById('loadDataButton');
        if (loadDataButton) {
            loadDataButton.style.display = isAdmin ? 'inline-flex' : 'none';
        }

        if (typeof renderAllPoles === 'function') renderAllPoles();
        if (typeof renderAllTurbines === 'function') renderAllTurbines();
    }

    function checkAdminPermission() {
        if (userRole !== 'admin') {
            showNotification('Access denied. Admin privileges required.', 'error');
            return false;
        }
        return true;
    }

    // --- MAP INITIALIZATION AND DATA LOADING ---
    function initMap() {
        map = L.map('map').setView([-29.0, 24.0], 5);
        const cartoLight = L.tileLayer('https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png', {
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors &copy; <a href="https://carto.com/attributions">CARTO</a>',
            maxZoom: 19
        });
        cartoLight.addTo(map);
        L.control.layers({
            "CartoDB Light": cartoLight,
            "OpenStreetMap": L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', { maxZoom: 19 }),
            "Esri Satellite": L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', { maxZoom: 19 })
        }).addTo(map);

        map.on('click', (e) => {
            latitudeInput.value = e.latlng.lat.toFixed(5);
            longitudeInput.value = e.latlng.lng.toFixed(5);
        });

        // Set up map-specific event listeners now that map exists
        setupMapEventListeners();
    }

    async function loadDataFromServer() {
        try {
            showNotification('Loading data from server...', 'info');
            const response = await fetch('/api/mapdata');
            if (!response.ok) throw new Error(`Server responded with ${response.status}`);
            const data = await response.json();
            
            polesData = data.poles || [];
            turbinesData = data.turbines || [];
            renderAllPoles();
            renderAllTurbines();
            showNotification(`Loaded ${polesData.length} poles and ${turbinesData.length} turbines.`, 'success');
        } catch (error) {
            console.error('Error loading data from server:', error);
            showNotification('Failed to load data from server.', 'error');
        }
    }

    // --- ASSET (POLE/TURBINE) MANAGEMENT ---
    const pylonIcon = L.icon({ iconUrl: 'pylon.png', iconSize: [32, 32], iconAnchor: [16, 32], popupAnchor: [0, -32] });
    const turbineIcon = L.icon({ iconUrl: 'turbine.png', iconSize: [32, 32], iconAnchor: [16, 32], popupAnchor: [0, -32] });

    function displayPoleOnMap(pole) {
        const marker = L.marker([pole.latitude, pole.longitude], { icon: pylonIcon });
        const popupContent = `
            <p><strong>Pole Number:</strong> ${pole.poleNumber}</p>
            ${pole.thumbnailImagePath ? `<img src="${pole.thumbnailImagePath}" alt="Thumbnail" class="thumbnail-popup">` : ''}
            <button class="view-pdf-button">View PDF Report</button>
            ${userRole === 'admin' ? `<button class="delete-pole-button" data-pole-id="${pole.id}">Delete Pole</button>` : ''}
        `;
        marker.bindPopup(popupContent);
        marker.poleId = pole.id;
        marker.addTo(map);
        return marker;
    }

    function displayTurbineOnMap(turbine) {
        const marker = L.marker([turbine.latitude, turbine.longitude], { icon: turbineIcon });
        const popupContent = `
            <p><strong>Turbine Name:</strong> ${turbine.turbineName}</p>
            ${turbine.thumbnailImagePath ? `<img src="${turbine.thumbnailImagePath}" alt="Thumbnail" class="thumbnail-popup">` : ''}
            <button class="view-pdf-button">View PDF Report</button>
            ${userRole === 'admin' ? `<button class="delete-turbine-button" data-turbine-id="${turbine.id}">Delete Turbine</button>` : ''}
        `;
        marker.bindPopup(popupContent);
        marker.turbineId = turbine.id;
        marker.addTo(map);
        return marker;
    }

    function renderAllPoles() {
        mapMarkers.forEach(marker => map.removeLayer(marker));
        mapMarkers = polesData.map(displayPoleOnMap);
    }

    function renderAllTurbines() {
        turbineMarkers.forEach(marker => map.removeLayer(marker));
        turbineMarkers = turbinesData.map(displayTurbineOnMap);
    }

    async function addAsset(type, formData) {
        if (!checkAdminPermission()) return;

        const endpoint = type === 'pole' ? '/api/poles' : '/api/turbines';
        const successMessage = type === 'pole' ? 'Pole added successfully!' : 'Turbine added successfully!';

        showUploadProgress(type);
        uploadFormWithProgress(endpoint, formData,
            (percent) => updateUploadProgress(percent),
            (response) => {
                hideUploadProgress();
                if (response.success) {                    showNotification(successMessage, 'success');
                    loadDataFromServer(); // Refresh data to get the new item
                    if (type === 'pole') {
                        document.getElementById('poleForm').reset();
                    } else {
                        document.getElementById('turbineForm').reset();
                    }
                } else {
                    showNotification(`Error: ${response.message}`, 'error');
                }
            },
            (errorMsg) => {
                hideUploadProgress();
                showNotification(`Upload Error: ${errorMsg}`, 'error');
            }
        );
    }

    async function deleteAsset(type, id) {
        if (!checkAdminPermission()) return;
        const endpoint = type === 'pole' ? `/api/poles/${id}` : `/api/turbines/${id}`;
        const successMessage = type === 'pole' ? 'Pole deleted successfully!' : 'Turbine deleted successfully!';

        try {
            const response = await fetch(endpoint, { method: 'DELETE' });
            const result = await response.json();
            if (result.success) {
                showNotification(successMessage, 'success');
                loadDataFromServer(); // Easiest way to refresh state
            } else {
                showNotification(`Failed to delete: ${result.message}`, 'error');
            }
        } catch (error) {
            console.error(`Error deleting ${type}:`, error);
            showNotification(`Failed to delete ${type}.`, 'error');
        }
    }

    // --- FILE & DATA HANDLING ---
    function saveData() {
        if (polesData.length === 0 && turbinesData.length === 0) {
            showNotification('No data to save.', 'info');
            return;
        }
        const dataToSave = { poles: polesData, turbines: turbinesData };
        const blob = new Blob([JSON.stringify(dataToSave, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'map_data_backup.json';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        showNotification('Data backup downloaded.', 'success');
    }

    function loadData(event) {
        const file = event.target.files[0];
        if (!file) return;
        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const data = JSON.parse(e.target.result);
                // Basic validation
                if (data && (data.poles || data.turbines)) {
                    polesData = data.poles || [];
                    turbinesData = data.turbines || [];
                    renderAllPoles();
                    renderAllTurbines();
                    showNotification('Data restored from backup. Note: This is a local view. Save items individually to persist them on the server.', 'warning');
                } else {
                    throw new Error("Invalid file format.");
                }
            } catch (error) {
                showNotification(`Error loading backup: ${error.message}`, 'error');
            }
        };
        reader.readAsText(file);
        loadFileInput.value = '';
    }

    // --- UPLOAD PROGRESS UI ---
    function showUploadProgress(type) {
        document.getElementById('uploadProgressTitle').textContent = `Uploading ${type}...`;
        document.getElementById('uploadProgressContainer').style.display = 'block';
        updateUploadProgress(0);
    }

    function updateUploadProgress(percent) {
        document.getElementById('uploadProgressBar').style.width = `${percent}%`;
        document.getElementById('uploadProgressText').textContent = `${percent}%`;
    }

    function hideUploadProgress() {
        document.getElementById('uploadProgressContainer').style.display = 'none';
    }

    function uploadFormWithProgress(url, formData, onProgress, onSuccess, onError) {
        const xhr = new XMLHttpRequest();
        xhr.upload.addEventListener('progress', e => {
            if (e.lengthComputable) {
                onProgress(Math.round((e.loaded / e.total) * 100));
            }
        });
        xhr.addEventListener('load', () => {
            if (xhr.status >= 200 && xhr.status < 300) {
                onSuccess(JSON.parse(xhr.responseText));
            } else {
                onError(JSON.parse(xhr.responseText).message || 'Upload failed');
            }
        });
        xhr.addEventListener('error', () => onError('Network error'));
        xhr.open('POST', url, true);
        xhr.send(formData);
    }

    // --- PDF MODAL ---
    function openPdfModal(pdfPath) {
        if (pdfModal && pdfModalIframe) {
            pdfModalIframe.src = pdfPath;
            pdfModal.style.display = "block";
        }
    }

    function closePdfModal() {
        if (pdfModal) {
            pdfModal.style.display = "none";
            pdfModalIframe.src = "";
        }
    }

    // --- SEARCH FUNCTIONALITY ---
    function performSearch(query) {
        if (!query.trim()) {
            hideSearchResults();
            return;
        }

        const results = [];
        const searchTerm = query.toLowerCase().trim();

        // Search poles
        polesData.forEach(pole => {
            if (pole.poleNumber.toString().toLowerCase().includes(searchTerm)) {
                results.push({
                    type: 'pole',
                    id: pole.id,
                    title: `Pole ${pole.poleNumber}`,
                    details: `Lat: ${pole.latitude}, Lng: ${pole.longitude}`,
                    latitude: pole.latitude,
                    longitude: pole.longitude,
                    marker: mapMarkers.find(m => m.poleId === pole.id)
                });
            }
        });

        // Search turbines
        turbinesData.forEach(turbine => {
            if (turbine.turbineName.toLowerCase().includes(searchTerm)) {
                results.push({
                    type: 'turbine',
                    id: turbine.id,
                    title: `Turbine ${turbine.turbineName}`,
                    details: `Lat: ${turbine.latitude}, Lng: ${turbine.longitude}`,
                    latitude: turbine.latitude,
                    longitude: turbine.longitude,
                    marker: turbineMarkers.find(m => m.turbineId === turbine.id)
                });
            }
        });

        displaySearchResults(results);
    }

    function displaySearchResults(results) {
        const searchResultsContainer = document.getElementById('searchResults');
        
        if (results.length === 0) {
            searchResultsContainer.innerHTML = '<div class="search-no-results">No assets found matching your search.</div>';
        } else {
            searchResultsContainer.innerHTML = results.map(result => `
                <div class="search-result-item" data-lat="${result.latitude}" data-lng="${result.longitude}" data-marker-id="${result.id}" data-type="${result.type}">
                    <i class="search-result-icon ${result.type} fas ${result.type === 'pole' ? 'fa-broadcast-tower' : 'fa-fan'}"></i>
                    <div class="search-result-info">
                        <div class="search-result-title">${result.title}</div>
                        <div class="search-result-details">${result.details}</div>
                    </div>
                </div>
            `).join('');
        }
        
        searchResultsContainer.style.display = 'block';
    }

    function hideSearchResults() {
        const searchResultsContainer = document.getElementById('searchResults');
        searchResultsContainer.style.display = 'none';
    }

    function navigateToAsset(latitude, longitude, markerId, type) {
        // Zoom to the asset location
        map.setView([latitude, longitude], 15);
        
        // Find and open the marker popup
        const markers = type === 'pole' ? mapMarkers : turbineMarkers;
        const marker = markers.find(m => 
            (type === 'pole' && m.poleId == markerId) || 
            (type === 'turbine' && m.turbineId == markerId)
        );
        
        if (marker) {
            // Highlight the marker with a bounce animation
            setTimeout(() => {
                marker.openPopup();
                // Add a temporary highlight effect
                const markerElement = marker.getElement();
                if (markerElement) {
                    markerElement.style.animation = 'markerBounce 1s ease-in-out';
                    setTimeout(() => {
                        markerElement.style.animation = '';
                    }, 1000);
                }
            }, 500);
        }
        
        hideSearchResults();
        showNotification(`Navigated to ${type} location`, 'success');
    }

    // --- EVENT LISTENERS ---
    function setupEventListeners() {        loginForm.addEventListener('submit', (e) => {
            e.preventDefault();
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const user = users[username];
            if (user && user.password === password) {
                localStorage.setItem('currentUser', JSON.stringify({ username, role: user.role, name: user.name }));
                loginUser(username, user.role, user.name);
            } else {
                showNotification('Invalid username or password.', 'error');
            }
        });

        logoutBtn.addEventListener('click', logout);        addPoleButton.addEventListener('click', () => {
            const formData = new FormData();
            formData.append('poleNumber', poleNumberInput.value);
            formData.append('latitude', latitudeInput.value);
            formData.append('longitude', longitudeInput.value);
            formData.append('pdfReport', pdfReportInput.files[0]);
            formData.append('thumbnailImage', thumbnailImageInput.files[0]);
            addAsset('pole', formData);
        });

        addTurbineButton.addEventListener('click', () => {
            const formData = new FormData();
            formData.append('turbineName', turbineNameInput.value);
            formData.append('latitude', turbineLatitudeInput.value);
            formData.append('longitude', turbineLongitudeInput.value);
            formData.append('pdfReport', turbinePdfReportInput.files[0]);
            formData.append('thumbnailImage', turbineThumbnailImageInput.files[0]);
            addAsset('turbine', formData);
        });

        saveDataButton.addEventListener('click', saveData);
        loadDataButton.addEventListener('click', () => loadFileInput.click());
        loadFileInput.addEventListener('change', loadData);
        refreshDataButton.addEventListener('click', loadDataFromServer);

        poleTabBtn.addEventListener('click', () => switchAssetTab('pole'));
        turbineTabBtn.addEventListener('click', () => switchAssetTab('turbine'));

        modalCloseButton.addEventListener('click', closePdfModal);
        window.addEventListener('click', (event) => {
            if (event.target === pdfModal) closePdfModal();
        });

        // Note: Map event listeners will be set up when map is initialized
    }

    function setupMapEventListeners() {
        // Delegated event listeners for dynamically created buttons in popups
        map.on('popupopen', (e) => {
            const popupNode = e.popup.getContentNode();
            const viewPdfBtn = popupNode.querySelector('.view-pdf-button');
            const deletePoleBtn = popupNode.querySelector('.delete-pole-button');
            const deleteTurbineBtn = popupNode.querySelector('.delete-turbine-button');

            if (viewPdfBtn) {
                viewPdfBtn.addEventListener('click', () => {
                    // Find the correct pole or turbine based on the marker
                    const marker = e.popup._source;
                    let pdfPath = null;
                    
                    if (marker.poleId) {
                        // This is a pole marker
                        const pole = polesData.find(p => p.id == marker.poleId);
                        if (pole && pole.reportPdfPath) {
                            pdfPath = pole.reportPdfPath;
                        }
                    } else if (marker.turbineId) {
                        // This is a turbine marker
                        const turbine = turbinesData.find(t => t.id == marker.turbineId);
                        if (turbine && turbine.reportPdfPath) {
                            pdfPath = turbine.reportPdfPath;
                        }
                    }
                    
                    if (pdfPath) {
                        openPdfModal(pdfPath);
                    } else {
                        showNotification('No PDF report available for this asset.', 'warning');
                    }
                });
            }

            if (deletePoleBtn) {
                deletePoleBtn.addEventListener('click', (ev) => {
                    if (confirm('Are you sure you want to delete this pole?')) {
                        deleteAsset('pole', ev.target.dataset.poleId);
                    }
                });
            }

            if (deleteTurbineBtn) {
                deleteTurbineBtn.addEventListener('click', (ev) => {
                    if (confirm('Are you sure you want to delete this turbine?')) {
                        deleteAsset('turbine', ev.target.dataset.turbineId);
                    }
                });
            }
        });

        // Search functionality event listeners
        if (assetSearchInput && searchButton) {
            // Search on input (live search)
            assetSearchInput.addEventListener('input', (e) => {
                performSearch(e.target.value);
            });

            // Search on button click
            searchButton.addEventListener('click', () => {
                performSearch(assetSearchInput.value);
            });

            // Search on Enter key
            assetSearchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    performSearch(assetSearchInput.value);
                }
            });

            // Handle clicking on search results
            searchResults.addEventListener('click', (e) => {
                const resultItem = e.target.closest('.search-result-item');
                if (resultItem) {
                    const lat = parseFloat(resultItem.dataset.lat);
                    const lng = parseFloat(resultItem.dataset.lng);
                    const markerId = resultItem.dataset.markerId;
                    const type = resultItem.dataset.type;
                    
                    navigateToAsset(lat, lng, markerId, type);
                    assetSearchInput.value = '';
                }
            });

            // Hide search results when clicking outside
            document.addEventListener('click', (e) => {
                if (!e.target.closest('.search-container')) {
                    hideSearchResults();
                }
            });

            // Hide search results when pressing Escape
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape') {
                    hideSearchResults();
                    assetSearchInput.blur();
                }
            });
        }
    }

    function switchAssetTab(type) {
        const poleForm = document.getElementById('poleForm');
        const turbineForm = document.getElementById('turbineForm');
        if (type === 'pole') {
            poleTabBtn.classList.add('active');
            turbineTabBtn.classList.remove('active');
            poleForm.classList.add('active');
            turbineForm.classList.remove('active');
        } else {
            turbineTabBtn.classList.add('active');
            poleTabBtn.classList.remove('active');
            turbineForm.classList.add('active');
            poleForm.classList.remove('active');
        }
    }

    // --- APPLICATION INITIALIZATION ---
    async function initializeApp() {
        // Setup non-map listeners first
        setupEventListeners();
        
        const savedUser = localStorage.getItem('currentUser');
        if (savedUser) {
            const userData = JSON.parse(savedUser);
            await loginUser(userData.username, userData.role, userData.name);
        } else {
            loginPage.style.display = 'flex';
            mainApp.style.display = 'none';
        }
    }

    initializeApp();
});
