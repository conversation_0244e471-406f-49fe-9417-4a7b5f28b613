/* Import Google Fonts for better typography */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* Modern CSS Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-blue: #2563eb;
    --secondary-blue: #1e40af;
    --accent-red: #dc2626;
    --silver: #94a3b8;
    --dark-silver: #64748b;
    --text-dark: #0f172a;
    --text-medium: #334155;
    --text-light: #64748b;
    --background-light: #f8fafc;
    --background-white: #ffffff;
    --border-light: #e2e8f0;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 20px 25px -5px rgb(0 0 0 / 0.1);
    --border-radius: 8px;
    --transition: all 0.2s ease;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: var(--background-light);
    color: var(--text-dark);
    line-height: 1.6;
    min-height: 100vh;
}

/* Professional Industrial Login Page */
.login-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 25%, #334155 75%, #475569 100%);
    padding: 20px;
    position: relative;
    overflow: hidden;
}

.login-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
        radial-gradient(circle at 20% 80%, rgba(37, 99, 235, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(148, 163, 184, 0.08) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(220, 38, 38, 0.06) 0%, transparent 50%);
    pointer-events: none;
}

.login-card {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    box-shadow: 
        0 25px 50px -12px rgba(0, 0, 0, 0.5),
        0 0 0 1px rgba(148, 163, 184, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    padding: 3rem;
    width: 100%;
    max-width: 520px;
    text-align: center;
    animation: slideInUp 0.8s ease;
    position: relative;
    border: 1px solid rgba(148, 163, 184, 0.3);
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(40px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.login-header {
    margin-bottom: 2.5rem;
}

.company-logos {
    display: flex;
    align-items: stretch;
    justify-content: center;
    gap: 1.2rem;
    margin-bottom: 2.5rem;
    width: 100%;
}

.ads-logo, .brandsa-logo, .redrocket-logo {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 1.2rem 1rem;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-radius: 12px;
    border: 2px solid #e2e8f0;
    flex: 1;
    min-width: 0;
    max-width: 180px;
    min-height: 140px;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.ads-logo:hover, .brandsa-logo:hover, .redrocket-logo:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.ads-logo::before, .brandsa-logo::before, .redrocket-logo::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
}

.ads-logo {
    color: var(--primary-blue);
}

.ads-logo::before {
    background: linear-gradient(90deg, var(--primary-blue), var(--secondary-blue));
}

.brandsa-logo {
    color: var(--text-dark);
}

.brandsa-logo::before {
    background: linear-gradient(90deg, var(--dark-silver), var(--silver));
}

.redrocket-logo {
    color: var(--accent-red);
}

.redrocket-logo::before {
    background: linear-gradient(90deg, var(--accent-red), #b91c1c);
}

.ads-logo i, .brandsa-logo i, .redrocket-logo i {
    font-size: 2.2rem;
    margin-bottom: 0.5rem;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.ads-logo span, .brandsa-logo span, .redrocket-logo span {
    font-weight: 700;
    font-size: 1rem;
    letter-spacing: 0.5px;
    text-transform: uppercase;
}

.ads-logo small, .brandsa-logo small, .redrocket-logo small {
    font-size: 0.75rem;
    color: var(--text-medium);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.partnership-indicator {
    display: none;
}

.login-header h1 {
    color: var(--text-dark);
    font-size: 2.25rem;
    margin-bottom: 0.75rem;
    font-weight: 800;
    letter-spacing: -0.5px;
    text-transform: uppercase;
    background: linear-gradient(135deg, var(--text-dark) 0%, var(--dark-silver) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.login-header p {
    color: var(--text-medium);
    font-size: 1.1rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1.5px;
    position: relative;
}

.login-header p::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-blue), var(--accent-red));
    border-radius: 2px;
}

.login-form {
    margin: 2.5rem 0;
}

.input-group {
    position: relative;
    margin-bottom: 1.5rem;
}

.input-group i {
    position: absolute;
    left: 18px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--dark-silver);
    z-index: 2;
    font-size: 1.1rem;
}

.input-group input {
    width: 100%;
    padding: 18px 18px 18px 55px;
    border: 2px solid var(--border-light);
    border-radius: 10px;
    font-size: 1rem;
    font-weight: 500;
    transition: all 0.3s ease;
    background: #ffffff;
    color: var(--text-dark);
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.input-group input:focus {
    outline: none;
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 4px rgba(37, 99, 235, 0.1), inset 0 1px 3px rgba(0, 0, 0, 0.1);
    background: #fafbff;
}

.input-group input::placeholder {
    color: var(--text-light);
    font-weight: 400;
}

.login-btn {
    width: 100%;
    padding: 18px;
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 50%, var(--accent-red) 100%);
    color: white;
    border: none;
    border-radius: 10px;
    font-size: 1.1rem;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    box-shadow: 0 8px 25px 0 rgba(37, 99, 235, 0.3);
    position: relative;
    overflow: hidden;
}

.login-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.login-btn:hover::before {
    left: 100%;
}

.login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 35px 0 rgba(37, 99, 235, 0.4);
}

.login-btn:active {
    transform: translateY(0);
}

.login-footer {
    margin-top: 2.5rem;
    padding-top: 2rem;
    border-top: 2px solid var(--border-light);
}

.login-footer p {
    color: var(--text-medium);
    font-size: 0.95rem;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.login-footer p i {
    color: var(--accent-red);
    font-size: 1.2rem;
}

.login-footer small {
    color: var(--text-light);
    font-size: 0.8rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.ads-logo span, .brandsa-logo span {
    font-weight: 700;
    font-size: 1rem;
}

.ads-logo small, .brandsa-logo small {
    font-size: 0.75rem;
    color: var(--text-medium);
    font-weight: 400;
}

.partnership-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    color: var(--text-medium);
    font-size: 0.875rem;
}

.partnership-indicator i {
    font-size: 1.25rem;
    margin-bottom: 0.25rem;
}

.login-header h1 {
    color: var(--text-dark);
    font-size: 1.75rem;
    margin-bottom: 0.5rem;
    font-weight: 700;
}

.login-header p {
    color: var(--text-medium);
    font-size: 1rem;
}

.login-form {
    margin: 2rem 0;
}

.input-group {
    position: relative;
    margin-bottom: 1rem;
}

.input-group i {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-medium);
    z-index: 1;
}

.input-group input {
    width: 100%;
    padding: 14px 14px 14px 45px;
    border: 2px solid var(--border-light);
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: var(--transition);
    background: var(--background-white);
}

.input-group input:focus {
    outline: none;
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgb(30 64 175 / 0.1);
}

.login-btn {
    width: 100%;
    padding: 14px;
    background: linear-gradient(45deg, var(--primary-blue), var(--secondary-blue));
    color: white;
    border: none;
    border-radius: var(--border-radius);
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.login-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.login-footer {
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid var(--border-light);
}

.login-footer p {
    color: var(--text-medium);
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.login-footer small {
    color: var(--text-light);
    font-size: 0.75rem;
}

/* Main Application Styles */
.main-app {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Enhanced Header */
.app-header {
    background: linear-gradient(45deg, var(--background-white), #f8fafc);
    border-bottom: 2px solid var(--border-light);
    padding: 1rem 1.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: var(--shadow-sm);
    position: relative;
    z-index: 1000;
}

.company-branding {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.ads-brand, .client-brand {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
}

.ads-brand {
    color: var(--primary-blue);
    font-size: 1.1rem;
}

.ads-brand i {
    font-size: 1.5rem;
}

.separator {
    color: var(--text-light);
    font-size: 1.25rem;
}

.client-brand {
    color: var(--accent-green);
    font-size: 1.1rem;
}

.client-brand i {
    font-size: 1.25rem;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 1rem;
    min-height: 50px;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: #f8fafc;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    color: #1f2937;
    font-weight: 500;
}

.user-info i {
    color: #64748b;
}

.user-role {
    background: #059669;
    color: white;
    padding: 0.25rem 0.6rem;
    border-radius: 6px;
    font-size: 0.75rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.user-role.admin {
    background: #2563eb;
}

.logout-btn {
    padding: 0.6rem 1.2rem;
    background: #dc2626;
    color: white;
    border: 2px solid #dc2626;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 8px rgba(220, 38, 38, 0.3);
}

.logout-btn:hover {
    background: #b91c1c;
    border-color: #b91c1c;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(220, 38, 38, 0.4);
}

/* FORCE LOGOUT BUTTON TEXT TO BE VISIBLE */
.logout-btn,
.logout-btn *,
#logoutBtn,
#logoutBtn *,
button#logoutBtn,
button#logoutBtn * {
    color: white !important;
    background-color: #dc2626 !important;
    text-decoration: none !important;
    opacity: 1 !important;
    visibility: visible !important;
}

.logout-btn i,
#logoutBtn i,
button#logoutBtn i {
    color: white !important;
}

/* Ensure button text is never transparent */
.header-right .logout-btn {
    background: #dc2626 !important;
    color: white !important;
    border: 2px solid #dc2626 !important;
}

.header-right .logout-btn:hover {
    background: #b91c1c !important;
    color: white !important;
    border: 2px solid #b91c1c !important;
}

/* Main Content */
main {
    flex: 1;
    display: flex;
    height: calc(100vh - 140px); /* Account for header and footer */
}

#map-container {
    flex: 1;
    position: relative;
}

#map {
    width: 100%;
    height: 100%;
}

/* Enhanced Controls Panel */
#controls-panel {
    width: 380px;
    background: var(--background-white);
    border-left: 2px solid var(--border-light);
    padding: 1.5rem;
    overflow-y: auto;
    box-shadow: var(--shadow-md);
}

/* Asset Type Selector */
.asset-type-selector {
    margin-bottom: 1.5rem;
}

.asset-type-selector h2 {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
    color: var(--text-dark);
    font-size: 1.25rem;
    font-weight: 600;
}

.asset-tabs {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.asset-tab {
    flex: 1;
    padding: 0.75rem;
    border: 2px solid var(--border-light);
    background: var(--background-white);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.875rem;
    font-weight: 500;
}

.asset-tab:hover {
    border-color: var(--primary-blue);
    background: #eff6ff;
}

.asset-tab.active {
    border-color: var(--primary-blue);
    background: var(--primary-blue);
    color: white;
}

.asset-tab i {
    font-size: 1.25rem;
}

/* Asset Forms */
.asset-form {
    display: none;
    animation: fadeIn 0.3s ease;
}

.asset-form.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.asset-form h3 {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
    color: var(--text-dark);
    font-size: 1.1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--border-light);
}

.form-group {
    margin-bottom: 1rem;
}

.form-row {
    display: flex;
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.form-row .form-group {
    flex: 1;
    margin-bottom: 0;
}

.form-group label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-dark);
    font-size: 0.875rem;
}

.form-group input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-light);
    border-radius: var(--border-radius);
    font-size: 0.875rem;
    transition: var(--transition);
    background: var(--background-white);
}

.form-group input:focus {
    outline: none;
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgb(30 64 175 / 0.1);
}

.form-group input[type="file"] {
    padding: 0.5rem;
    border: 2px dashed var(--border-light);
    background: var(--background-light);
    cursor: pointer;
}

.form-group input[type="file"]:hover {
    border-color: var(--primary-blue);
    background: #eff6ff;
}

.add-btn {
    width: 100%;
    padding: 0.875rem;
    background: linear-gradient(45deg, var(--accent-green), #059669);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    font-weight: 600;
    margin-top: 1rem;
}

.add-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* Data Management */
.data-management h3 {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
    color: var(--text-dark);
    font-size: 1.1rem;
    font-weight: 600;
}

.data-buttons {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.data-btn {
    padding: 0.75rem;
    border: 1px solid var(--border-light);
    background: var(--background-white);
    color: var(--text-dark);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
}

.data-btn:hover {
    background: var(--background-light);
    border-color: var(--primary-blue);
    transform: translateY(-1px);
}

/* Admin Controls */
.admin-controls h3 {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
    color: var(--error-red);
    font-size: 1.1rem;
    font-weight: 600;
}

.admin-warning {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem;
    background: #fef2f2;
    border: 1px solid #fecaca;
    border-radius: var(--border-radius);
    color: var(--error-red);
    font-size: 0.875rem;
    margin-bottom: 1rem;
}

/* Enhanced Footer */
footer {
    background: var(--background-white);
    border-top: 1px solid var(--border-light);
    padding: 1rem 1.5rem;
    box-shadow: var(--shadow-sm);
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.footer-left p {
    color: var(--text-dark);
    font-weight: 500;
}

.footer-right {
    text-align: right;
}

.footer-right p {
    color: var(--text-medium);
    font-size: 0.875rem;
}

.footer-right small {
    color: var(--text-light);
    font-size: 0.75rem;
}

header {
    background-color: #ffffff;
    color: #333333;
    padding: 1rem 1.5rem;
    text-align: left;
    border-bottom: 1px solid #e0e0e0;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

header h1 {
    margin: 0;
    font-size: 1.5em;
    font-weight: 600;
}

.powered-by {
    font-size: 0.85em;
    color: #6c757d;
    margin-top: 0.25rem;
}

main {
    display: flex;
    flex-grow: 1;
    padding: 10px;
    gap: 10px;
}

#map-container {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

#map {
    height: 100%; /* Will take height of its container */
    min-height: 500px; /* Minimum height for the map */
    border: 1px solid #e0e0e0; /* Lighter border */
    border-radius: 8px; /* Slightly more rounded */
}

#controls-panel {
    width: 320px; /* Slightly wider */
    padding: 20px;
    border: none; /* Remove border, rely on shadow */
    background-color: #f8f9fa; /* Lighter background */
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 15px; /* Space between sections */
}

#controls-panel h2 {
    margin-top: 0;
    font-size: 1.1em; /* Slightly smaller */
    font-weight: 600; /* Bolder */
    color: #343a40; /* Darker text */
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 0.6em;
    margin-bottom: 0.5em; /* Reduced margin */
}

#controls-panel div {
    margin-bottom: 0; /* Handled by gap in parent */
}

#controls-panel label {
    display: block;
    margin-bottom: 6px; /* Slightly more space */
    font-weight: 500; /* Medium weight */
    font-size: 0.9em;
    color: #495057;
}

#controls-panel input[type="text"],
#controls-panel input[type="file"] {
    width: 100%; /* Full width */
    padding: 10px; /* More padding */
    margin-bottom: 0; /* Handled by gap in parent */
    border: 1px solid #ced4da;
    border-radius: 6px; /* More rounded */
    box-sizing: border-box;
    font-size: 0.95em;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

#controls-panel input[type="text"]:focus,
#controls-panel input[type="file"]:focus-within { /* file input needs focus-within */
    border-color: #80bdff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

#controls-panel input[type="file"] {
    padding: 8px; /* Adjust for file input */
}


#controls-panel button {
    width: 100%;
    padding: 10px 15px;
    margin-bottom: 0;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.95em;
    font-weight: 500;
    transition: background-color 0.2s ease, box-shadow 0.2s ease;
    box-sizing: border-box;
}

#controls-panel button:hover {
    background-color: #0069d9;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

#controls-panel button:active {
    background-color: #005cbf;
}


#controls-panel hr {
    margin: 15px 0; /* Adjusted margin */
    border: 0;
    border-top: 1px solid #e9ecef; /* Lighter hr */
}

footer {
    background-color: #333;
    color: white;
    text-align: center;
    padding: 1em 0;
    font-size: 0.9em;
}

/* Leaflet popup customizations */
.leaflet-popup-content-wrapper {
    border-radius: 5px;
}

.leaflet-popup-content {
    margin: 10px !important;
}

.leaflet-popup-content img.thumbnail-popup {
    max-width: 150px;
    max-height: 100px;
    display: block;
    margin: 0 auto 5px auto;
    border: 1px solid #ddd;
    border-radius: 3px;
}

.leaflet-popup-content p {
    margin: 5px 0;
}

.leaflet-popup-content button {
    padding: 5px 10px;
    background-color: #28a745;
    color: white;
    border: none;
    border-radius: 3px;
    cursor: pointer;
    display: block;
    width: 100%;
    text-align: center;
    margin-top: 5px;
}

.leaflet-popup-content button:hover {
    background-color: #218838;
}

.leaflet-popup-content button.delete-pole-button {
    background-color: #dc3545; /* Red for delete */
    margin-top: 8px; /* Add some space if there are multiple buttons */
}

.leaflet-popup-content button.delete-pole-button:hover {
    background-color: #c82333; /* Darker red on hover */
}

.leaflet-popup-content button.delete-turbine-button {
    background-color: #dc3545; /* Red for delete */
    margin-top: 8px;
}

.leaflet-popup-content button.delete-turbine-button:hover {
    background-color: #c82333; /* Darker red on hover */
}

/* Notification Area Styles */
#notification-area {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 10000; /* Ensure it's above other elements */
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.notification {
    padding: 12px 18px;
    border-radius: 5px;
    color: #fff;
    font-size: 0.9em;
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
    opacity: 0; /* Start hidden */
    transform: translateY(20px); /* Start off-screen */
    transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
    max-width: 300px;
}

.notification.show {
    opacity: 1;
    transform: translateY(0);
}

.notification.success {
    background-color: #28a745; /* Green for success */
}

.notification.error {
    background-color: #dc3545; /* Red for error */
}

.notification.info {
    background-color: #17a2b8;
}

/* Keep all existing modal, notification, and other styles */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
}

.modal-content {
    background-color: var(--background-white);
    margin: 5% auto;
    padding: 0;
    border-radius: var(--border-radius);
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    overflow: hidden;
    box-shadow: var(--shadow-lg);
}

.modal-close-button, .close {
    position: absolute;
    top: 15px;
    right: 20px;
    color: var(--text-light);
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    z-index: 1;
    background: var(--background-white);
    border-radius: 50%;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-md);
    border: none;
}

.modal-close-button:hover, .close:hover {
    color: var(--text-dark);
    background: var(--background-light);
}

#pdfModalIframe {
    width: 100%;
    height: 70vh;
    border: none;
}

/* Popup styles for markers */
.thumbnail-popup {
    max-width: 200px;
    max-height: 150px;
    border-radius: var(--border-radius);
    margin: 0.5rem 0;
}

.view-pdf-button, .delete-pole-button, .delete-turbine-button {
    padding: 0.5rem 1rem;
    margin: 0.25rem;
    border: 1px solid var(--border-light);
    background: var(--background-white);
    color: var(--text-dark);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    font-size: 0.875rem;
}

.view-pdf-button:hover {
    background: var(--primary-blue);
    color: white;
    border-color: var(--primary-blue);
}

.delete-pole-button:hover, .delete-turbine-button:hover {
    background: var(--error-red);
    color: white;
    border-color: var(--error-red);
}

/* Upload Progress */
#uploadProgressContainer {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
}

#uploadProgressContainer > div {
    background-color: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: var(--shadow-lg);
    width: 350px;
    text-align: center;
}

#uploadProgressBar {
    height: 100%;
    background: linear-gradient(45deg, var(--accent-green), #059669);
    transition: width 0.3s;
    border-radius: 15px;
}

/* Legacy modal support */
#openLoginBtn {
    background-color: var(--accent-green);
    color: white;
    padding: 14px 20px;
    margin: 8px 0;
    border: none;
    cursor: pointer;
    width: 100%;
    font-size: 16px;
    border-radius: var(--border-radius);
}

#logoutBtn {
    background-color: var(--error-red);
    color: white;
    padding: 10px 16px;
    border: none;
    cursor: pointer;
    border-radius: var(--border-radius);
    font-size: 14px;
}

/* Search Container Styling */
.search-container {
    position: relative;
    margin-right: 1rem;
}

.search-input-group {
    display: flex;
    align-items: center;
    background: white;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    padding: 0.75rem 1rem;
    min-width: 400px;
    transition: all 0.3s ease;
}

.search-input-group:focus-within {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.search-input-group i {
    color: #64748b;
    margin-right: 0.5rem;
}

.search-input-group input {
    flex: 1;
    border: none;
    outline: none;
    font-size: 1rem;
    color: #1f2937;
    padding: 0.25rem 0;
}

.search-input-group input::placeholder {
    color: #94a3b8;
}

.search-btn {
    background: #3b82f6;
    color: white;
    border: none;
    padding: 0.75rem 1rem;
    border-radius: 6px;
    cursor: pointer;
    margin-left: 0.5rem;
    transition: all 0.3s ease;
}

.search-btn:hover {
    background: #2563eb;
    transform: translateY(-1px);
}

.search-results {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 2px solid #e2e8f0;
    border-top: none;
    border-radius: 0 0 8px 8px;
    max-height: 300px;
    overflow-y: auto;
    z-index: 1000;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.search-result-item {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #f1f5f9;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.search-result-item:hover {
    background: #f8fafc;
    border-left: 4px solid #3b82f6;
}

.search-result-item:last-child {
    border-bottom: none;
}

.search-result-icon {
    font-size: 1.2rem;
    width: 20px;
    text-align: center;
}

.search-result-icon.pole {
    color: #059669;
}

.search-result-icon.turbine {
    color: #dc2626;
}

.search-result-info {
    flex: 1;
}

.search-result-title {
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.25rem;
}

.search-result-details {
    font-size: 0.8rem;
    color: #64748b;
}

.search-no-results {
    padding: 1rem;
    text-align: center;
    color: #64748b;
    font-style: italic;
}

/* Marker bounce animation */
@keyframes markerBounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

/* Mobile responsive */
@media (max-width: 768px) {
    .search-input-group {
        min-width: 300px;
    }
}

@media (max-width: 580px) {
    .search-container {
        order: -1;
        margin: 0 0 1rem 0;
        width: 100%;
    }
    
    .search-input-group {
        min-width: 100%;
    }
}

/* Company Logo Specific Styling - Straight Row Layout */

/* ADS - Professional Aviation Blue */
.ads-logo {
    background: linear-gradient(135deg, #1e40af 0%, #2563eb 100%) !important;
    color: white !important;
    border: 2px solid #3b82f6 !important;
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3) !important;
}

.ads-logo i {
    font-size: 2.2rem;
    color: #93c5fd;
    margin-bottom: 0.5rem;
}

.ads-logo span {
    font-weight: 600;
    font-size: 0.9rem;
    text-align: center;
    line-height: 1.2;
}

.ads-logo small {
    font-size: 0.7rem;
    color: #dbeafe;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* BrandSA - Corporate Black with Red Triangle */
.brandsa-logo {
    background: linear-gradient(135deg, #1f2937 0%, #**********%) !important;
    color: white !important;
    border: 2px solid #374151 !important;
    box-shadow: 0 8px 25px rgba(31, 41, 55, 0.4) !important;
}

.brandsa-triangle {
    font-size: 2.5rem;
    color: #dc2626;
    font-weight: bold;
    margin-bottom: 0.3rem;
    filter: drop-shadow(0 2px 4px rgba(220, 38, 38, 0.3));
    line-height: 1;
}

.brandsa-logo span {
    font-weight: 700;
    font-size: 1.1rem;
    text-align: center;
    color: white;
}

.brandsa-logo small {
    font-size: 0.7rem;
    color: #d1d5db;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Red Rocket Energy - Bold Red */
.redrocket-logo {
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%) !important;
    color: white !important;
    border: 2px solid #ef4444 !important;
    box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4) !important;
}

.redrocket-logo i {
    font-size: 2.2rem;
    color: #fca5a5;
    margin-bottom: 0.5rem;
    animation: rocketPulse 3s ease-in-out infinite;
}

@keyframes rocketPulse {
    0%, 100% { transform: scale(1) rotate(-45deg); }
    50% { transform: scale(1.05) rotate(-40deg); }
}

.redrocket-logo span {
    font-weight: 700;
    font-size: 0.9rem;
    text-align: center;
    line-height: 1.2;
}

.redrocket-logo small {
    font-size: 0.7rem;
    color: #fecaca;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Perfect Row Hover Effects */
.ads-logo:hover, .brandsa-logo:hover, .redrocket-logo:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.3);
}

.brandsa-logo:hover .brandsa-triangle {
    color: #ef4444;
    transform: scale(1.1);
}

/* Responsive - Keep in row until very small screens */
@media (min-width: 768px) {
    .company-logos {
        gap: 1.5rem;
    }
    
    .ads-logo, .brandsa-logo, .redrocket-logo {
        max-width: 200px;
    }
}

@media (max-width: 767px) and (min-width: 580px) {
    .company-logos {
        gap: 1rem;
    }
    
    .ads-logo, .brandsa-logo, .redrocket-logo {
        max-width: 150px;
        padding: 1rem 0.8rem;
    }
    
    .ads-logo span, .brandsa-logo span, .redrocket-logo span {
        font-size: 0.85rem;
    }
    
    .ads-logo small, .brandsa-logo small, .redrocket-logo small {
        font-size: 0.65rem;
    }
}

@media (max-width: 579px) {
    .company-logos {
        flex-direction: column;
        gap: 1rem;
        align-items: center;
    }
    
    .ads-logo, .brandsa-logo, .redrocket-logo {
        width: 100%;
        max-width: 280px;
        flex: none;
    }
}

/* Logo Image Sizing */
.company-logo-img {
    width: 50px;
    height: 50px;
    object-fit: contain;
    margin-bottom: 0.5rem;
}

.company-logo-img-small {
    width: 30px;
    height: 30px;
    object-fit: contain;
    margin-right: 0.5rem;
}
