const express = require('express');
const path = require('path');
const multer = require('multer');
const fs = require('fs');
const { Pool } = require('pg'); // Add PostgreSQL client
const app = express();
const PORT = process.env.PORT || 8080;

// Create data file for permanent storage - use /tmp for Railway compatibility
const isProduction = process.env.NODE_ENV === 'production';
const DATA_DIR = isProduction ? '/tmp/data' : path.join(__dirname, 'data');
const DATA_FILE = path.join(DATA_DIR, 'mapData.json');

// Ensure directories exist for file uploads - use /tmp for Railway compatibility
const pdfPylonDir = isProduction ? '/tmp/PDF/Pylon' : path.join(__dirname, 'PDF', 'Pylon');
const pdfTurbineDir = isProduction ? '/tmp/PDF/Turbine' : path.join(__dirname, 'PDF', 'Turbine');
const thumbnailDir = isProduction ? '/tmp/Thumbnail' : path.join(__dirname, 'Thumbnail');
const uploadDir = isProduction ? '/tmp/uploads' : path.join(__dirname, 'uploads');

// Set up PostgreSQL connection - try ALL possible Railway environment variables
console.log('🔍 Checking ALL Railway database environment variables:');
console.log('DATABASE_URL available:', !!process.env.DATABASE_URL);
console.log('DATABASE_PUBLIC_URL available:', !!process.env.DATABASE_PUBLIC_URL);
console.log('POSTGRES_URL available:', !!process.env.POSTGRES_URL);
console.log('PGHOST:', process.env.PGHOST || 'Not set');
console.log('PGDATABASE:', process.env.PGDATABASE || 'Not set');
console.log('PGUSER:', process.env.PGUSER || 'Not set');
console.log('PGPORT:', process.env.PGPORT || 'Not set');
console.log('PGPASSWORD available:', !!process.env.PGPASSWORD);

// Determine which database URL to use - try multiple sources
let databaseUrl = null;
let dbHost = 'unknown';
let urlSource = 'none';

// Try DATABASE_PUBLIC_URL first (preferred for Railway)
if (process.env.DATABASE_PUBLIC_URL) {
  databaseUrl = process.env.DATABASE_PUBLIC_URL;
  urlSource = 'DATABASE_PUBLIC_URL (public proxy)';
} 
// Try DATABASE_URL second
else if (process.env.DATABASE_URL) {
  databaseUrl = process.env.DATABASE_URL;
  urlSource = 'DATABASE_URL (internal)';
} 
// Try POSTGRES_URL third
else if (process.env.POSTGRES_URL) {
  databaseUrl = process.env.POSTGRES_URL;
  urlSource = 'POSTGRES_URL';
}
// Try to construct from individual PG variables fourth
else if (process.env.PGHOST && process.env.PGDATABASE && process.env.PGUSER && process.env.PGPASSWORD) {
  const port = process.env.PGPORT || '5432';
  databaseUrl = `postgresql://${process.env.PGUSER}:${process.env.PGPASSWORD}@${process.env.PGHOST}:${port}/${process.env.PGDATABASE}`;
  urlSource = 'Constructed from PG* variables';
}

if (databaseUrl) {
  // Extract host from URL for logging
  try {
    const url = new URL(databaseUrl);
    dbHost = url.hostname;
  } catch (e) {
    dbHost = urlSource;
  }
  console.log(`✅ Using database URL from: ${urlSource}`);
  console.log(`🔗 Database host: ${dbHost}`);
} else {
  console.warn('❌ No database URL found - PostgreSQL disabled, using file-only mode');
}

let pool = null;

if (databaseUrl) {
  pool = new Pool({
    connectionString: databaseUrl,
    // Required for Railway PostgreSQL connections
    ssl: {
      rejectUnauthorized: false
    },
    // Add connection pool settings for better stability
    max: 20, // Maximum number of clients in the pool
    idleTimeoutMillis: 30000, // How long a client is allowed to remain idle before being closed
    connectionTimeoutMillis: 2000 // How long to wait for a connection to become available
  });

  // Database connection events
  pool.on('error', (err) => {
    console.error('Unexpected error on idle client', err);
  });

  pool.on('connect', () => {
    console.log(`Connected to PostgreSQL database at: ${dbHost}`);
  });
}

// Create directories if they don't exist
[pdfPylonDir, pdfTurbineDir, thumbnailDir, DATA_DIR, uploadDir].forEach(dir => {
  if (!fs.existsSync(dir)) {
    try {
      fs.mkdirSync(dir, { recursive: true });
      console.log(`Created directory: ${dir}`);
    } catch (error) {
      console.error(`Error creating directory ${dir}:`, error);
    }
  }
});

// Initialize database tables
async function initDatabase() {
  if (!pool) {
    console.log('Skipping database initialization - no PostgreSQL connection');
    return;
  }
  
  try {
    // Create poles table
    await pool.query(`
      CREATE TABLE IF NOT EXISTS poles (
        id BIGINT PRIMARY KEY,
        pole_number TEXT NOT NULL,
        latitude DECIMAL(10, 6) NOT NULL,
        longitude DECIMAL(10, 6) NOT NULL,
        report_pdf_path TEXT,
        thumbnail_image_path TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);
    
    // Create turbines table
    await pool.query(`
      CREATE TABLE IF NOT EXISTS turbines (
        id TEXT PRIMARY KEY,
        turbine_name TEXT NOT NULL,
        latitude DECIMAL(10, 6) NOT NULL,
        longitude DECIMAL(10, 6) NOT NULL,
        report_pdf_path TEXT,
        thumbnail_image_path TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);
    
    console.log('Database tables initialized');
  } catch (error) {
    console.error('Error initializing database:', error);
  }
}

// Initialize data file if it doesn't exist
if (!fs.existsSync(DATA_FILE)) {  try {
    fs.writeFileSync(DATA_FILE, JSON.stringify({ poles: [], turbines: [] }));
    console.log(`Created data file: ${DATA_FILE}`);
  } catch (error) {
    console.error(`Error creating data file ${DATA_FILE}:`, error);
  }
}

// Configure multer storage
const storage = multer.diskStorage({
  destination: function(req, file, cb) {
    try {
      if (file.fieldname === 'pdfReport') {
        // Determine if it's a pylon or turbine based on the form data
        const uploadType = req.body.type || 'pylon';
        cb(null, uploadType === 'turbine' ? pdfTurbineDir : pdfPylonDir);
      } else if (file.fieldname === 'thumbnailImage') {
        cb(null, thumbnailDir);
      } else {
        // Default location if fieldname doesn't match expected values
        cb(null, uploadDir);
      }
    } catch (error) {
      console.error('Error in multer destination function:', error);
      cb(error);
    }
  },
  filename: function(req, file, cb) {
    try {
      // Use original filename with timestamp to avoid duplicates
      const itemName = req.body.poleNumber || req.body.turbineName || Date.now().toString();
      const fileExtension = path.extname(file.originalname) || '.pdf';
      cb(null, `${itemName.replace(/\s+/g, '_')}_${Date.now()}${fileExtension}`);
    } catch (error) {
      console.error('Error in multer filename function:', error);
      cb(error);
    }
  }
});

const upload = multer({ 
  storage: storage,
  limits: {
    fileSize: 500 * 1024 * 1024, // Increased to 500MB file size limit
  }
}).fields([
  { name: 'pdfReport', maxCount: 1 },
  { name: 'thumbnailImage', maxCount: 1 }
]);

// Body parser middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Serve static files from the current directory
app.use(express.static(__dirname));

// Serve uploaded files - with different paths depending on environment
const pdfPath = isProduction ? '/tmp/PDF' : path.join(__dirname, 'PDF');
const thumbnailPath = isProduction ? '/tmp/Thumbnail' : path.join(__dirname, 'Thumbnail');
app.use('/PDF', express.static(pdfPath));
app.use('/Thumbnail', express.static(thumbnailPath));

// Helper function to read map data from PostgreSQL
async function getMapData() {
  // Only try PostgreSQL if pool exists
  if (pool) {
    try {
      // Get poles data
      const polesResult = await pool.query('SELECT * FROM poles ORDER BY created_at DESC');
      
      // Get turbines data
      const turbinesResult = await pool.query('SELECT * FROM turbines ORDER BY created_at DESC');
      
      // Convert DB column names to camelCase for frontend
      const poles = polesResult.rows.map(pole => ({
        id: Number(pole.id),
        poleNumber: pole.pole_number,
        latitude: Number(pole.latitude),
        longitude: Number(pole.longitude),
        reportPDFPath: pole.report_pdf_path,
        thumbnailImagePath: pole.thumbnail_image_path,
        createdAt: pole.created_at
      }));
      
      const turbines = turbinesResult.rows.map(turbine => ({
        id: turbine.id,
        turbineName: turbine.turbine_name,
        latitude: Number(turbine.latitude),
        longitude: Number(turbine.longitude),
        reportPDFPath: turbine.report_pdf_path,
        thumbnailImagePath: turbine.thumbnail_image_path
      }));
      
      return { poles, turbines };
    } catch (error) {
      console.error('Error reading map data from PostgreSQL:', error);
      console.log('Using file-based data fallback');
    }
  }
  
  // Fallback to file-based data (either no pool or PostgreSQL failed)
  try {
    if (fs.existsSync(DATA_FILE)) {
      const data = fs.readFileSync(DATA_FILE, 'utf8');
      return JSON.parse(data);
    }
  } catch (fileError) {
    console.error('Error reading fallback data file:', fileError);
  }
  
  return { poles: [], turbines: [] };
}

// Health check endpoint for Railway
app.get('/health', async (req, res) => {
  try {
    let dbConnected = false;
    let dbTime = null;
    
    // Test database connection only if pool exists
    if (pool) {
      try {
        const dbResult = await pool.query('SELECT NOW() as time');
        dbConnected = !!dbResult.rows[0];
        dbTime = dbResult.rows[0]?.time;
      } catch (dbError) {
        console.error('Database health check failed:', dbError);
        dbConnected = false;
      }
    }
    
    res.status(200).json({ 
      status: 'ok', 
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'development',
      directories: {
        dataDir: fs.existsSync(DATA_DIR),
        pdfPylon: fs.existsSync(pdfPylonDir),
        pdfTurbine: fs.existsSync(pdfTurbineDir),
        thumbnail: fs.existsSync(thumbnailDir)
      },
      database: {
        poolAvailable: !!pool,
        connected: dbConnected,
        time: dbTime
      }
    });
  } catch (error) {
    res.status(500).json({
      status: 'error',
      message: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// API endpoint to get all map data
app.get('/api/mapdata', async (req, res) => {
  try {
    const mapData = await getMapData();
    res.json(mapData);
  } catch (error) {
    console.error('Error fetching map data:', error);
    res.status(500).json({ success: false, message: 'Error fetching map data', error: error.message });
  }
});

// API endpoint for handling pole uploads
app.post('/api/poles', (req, res) => {
  try {
    console.log('Pole upload request received');
    
    upload(req, res, async function(err) {
      if (err) {
        console.error('Multer error:', err);
        // Check for file size error specifically
        if (err.code === 'LIMIT_FILE_SIZE') {
          return res.status(413).json({ 
            success: false, 
            message: 'File too large. The maximum file size is 500MB.',
            error: err.message
          });
        }
        return res.status(500).json({ 
          success: false, 
          message: 'Error uploading files',
          error: err.message 
        });
      }
      
      console.log('Files received:', req.files);
      console.log('Body received:', req.body);
      
      // Check if we have files
      if (!req.files || !req.files.pdfReport) {
        return res.status(400).json({ 
          success: false, 
          message: 'PDF file is required' 
        });
      }

      const pdfFile = req.files.pdfReport[0];
      const thumbnailFile = req.files.thumbnailImage ? req.files.thumbnailImage[0] : null;
      
      // Get the file paths relative to our server - adjust path for production
      const pdfPath = '/PDF/' + (isProduction ? 'Pylon/' : 'Pylon/') + pdfFile.filename;
      const thumbnailPath = thumbnailFile ? '/Thumbnail/' + thumbnailFile.filename : null;
      
      console.log('PDF path:', pdfPath);
      console.log('Thumbnail path:', thumbnailPath);
      
      // Create new pole object with a unique ID
      const poleId = Date.now();
      const poleNumber = req.body.poleNumber;
      const latitude = parseFloat(req.body.latitude);
      const longitude = parseFloat(req.body.longitude);
      
      // Create pole object for response
      const newPole = {
        id: poleId,
        poleNumber: poleNumber,
        latitude: latitude,
        longitude: longitude,
        reportPDFPath: pdfPath,
        thumbnailImagePath: thumbnailPath
      };
      
      // Try to save to both database and file storage for redundancy
      let dbSuccess = false;
      let fileSuccess = false;
        // Try PostgreSQL first (only if pool exists)
      if (pool) {
        try {
          await pool.query(
            'INSERT INTO poles(id, pole_number, latitude, longitude, report_pdf_path, thumbnail_image_path) VALUES($1, $2, $3, $4, $5, $6)',
            [poleId, poleNumber, latitude, longitude, pdfPath, thumbnailPath]
          );
          dbSuccess = true;
          console.log('Pole saved to database successfully');
        } catch (dbError) {
          console.error('Database error when adding pole:', dbError);
          // Continue to file storage on DB error
        }
      }
      
      // Always save to file as a fallback
      try {
        const mapData = fs.existsSync(DATA_FILE) ? 
          JSON.parse(fs.readFileSync(DATA_FILE, 'utf8')) : 
          { poles: [], turbines: [] };
        
        mapData.poles.push(newPole);
        fs.writeFileSync(DATA_FILE, JSON.stringify(mapData, null, 2));
        fileSuccess = true;
        console.log('Pole saved to file successfully');
      } catch (fileError) {
        console.error('Error saving to backup file:', fileError);
      }
      
      // Return success if at least one storage method worked
      if (dbSuccess || fileSuccess) {
        res.json({
          success: true,
          message: 'Pole added successfully',
          data: newPole,
          storage: dbSuccess ? (fileSuccess ? 'database and file' : 'database only') : 'file only'
        });
      } else {
        // Both storage methods failed
        res.status(500).json({
          success: false,
          message: 'Failed to save pole data to either database or file system'
        });
      }
    });
  } catch (error) {
    console.error('Error adding pole:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Error adding pole',
      error: error.message
    });
  }
});

// API endpoint for handling turbine uploads
app.post('/api/turbines', (req, res) => {
  try {
    console.log('Turbine upload request received');
    
    upload(req, res, async function(err) {
      if (err) {
        console.error('Multer error:', err);
        // Check for file size error specifically
        if (err.code === 'LIMIT_FILE_SIZE') {
          return res.status(413).json({ 
            success: false, 
            message: 'File too large. The maximum file size is 500MB.',
            error: err.message
          });
        }
        return res.status(500).json({ 
          success: false, 
          message: 'Error uploading files',
          error: err.message 
        });
      }
      
      console.log('Files received:', req.files);
      console.log('Body received:', req.body);
      
      // Check if we have files
      if (!req.files || !req.files.pdfReport) {
        return res.status(400).json({ 
          success: false, 
          message: 'PDF file is required' 
        });
      }

      const pdfFile = req.files.pdfReport[0];
      const thumbnailFile = req.files.thumbnailImage ? req.files.thumbnailImage[0] : null;
      
      // Get the file paths relative to our server - adjust path for production
      const pdfPath = '/PDF/' + (isProduction ? 'Turbine/' : 'Turbine/') + pdfFile.filename;
      const thumbnailPath = thumbnailFile ? '/Thumbnail/' + thumbnailFile.filename : null;
      
      // Create unique ID for turbine
      const turbineId = Date.now() + '_turbine';
      const turbineName = req.body.turbineName;
      const latitude = parseFloat(req.body.latitude);
      const longitude = parseFloat(req.body.longitude);
      
      // Create turbine object for response
      const newTurbine = {
        id: turbineId,
        turbineName: turbineName,
        latitude: latitude,
        longitude: longitude,
        reportPDFPath: pdfPath,
        thumbnailImagePath: thumbnailPath
      };
      
      // Try to save to both database and file storage for redundancy
      let dbSuccess = false;
      let fileSuccess = false;
        // Try PostgreSQL first (only if pool exists)
      if (pool) {
        try {
          await pool.query(
            'INSERT INTO turbines(id, turbine_name, latitude, longitude, report_pdf_path, thumbnail_image_path) VALUES($1, $2, $3, $4, $5, $6)',
            [turbineId, turbineName, latitude, longitude, pdfPath, thumbnailPath]
          );
          dbSuccess = true;
          console.log('Turbine saved to database successfully');
        } catch (dbError) {
          console.error('Database error when adding turbine:', dbError);
          // Continue to file storage on DB error
        }
      }
      
      // Always save to file as a fallback
      try {
        const mapData = fs.existsSync(DATA_FILE) ? 
          JSON.parse(fs.readFileSync(DATA_FILE, 'utf8')) : 
          { poles: [], turbines: [] };
        
        mapData.turbines.push(newTurbine);
        fs.writeFileSync(DATA_FILE, JSON.stringify(mapData, null, 2));
        fileSuccess = true;
        console.log('Turbine saved to file successfully');
      } catch (fileError) {
        console.error('Error saving to backup file:', fileError);
      }
      
      // Return success if at least one storage method worked
      if (dbSuccess || fileSuccess) {
        res.json({
          success: true,
          message: 'Turbine added successfully',
          data: newTurbine,
          storage: dbSuccess ? (fileSuccess ? 'database and file' : 'database only') : 'file only'
        });
      } else {
        // Both storage methods failed
        res.status(500).json({
          success: false,
          message: 'Failed to save turbine data to either database or file system'
        });
      }
    });
  } catch (error) {
    console.error('Error adding turbine:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Error adding turbine',
      error: error.message
    });
  }
});

// API endpoint for deleting a pole
app.delete('/api/poles/:id', async (req, res) => {
  try {
    const poleId = req.params.id;
    console.log('Attempting to delete pole with ID:', poleId, 'Type:', typeof poleId);
    
    let deleted = false;
    
    // Delete from PostgreSQL if available
    if (pool) {
      try {
        const result = await pool.query('DELETE FROM poles WHERE id = $1 RETURNING *', [poleId]);
        if (result.rowCount > 0) {
          deleted = true;
          console.log('Pole deleted from database');
        }
      } catch (dbError) {
        console.error('Database error when deleting pole:', dbError);
      }
    }
    
    // Also update file-based backup if it exists
    try {
      if (fs.existsSync(DATA_FILE)) {
        const mapData = JSON.parse(fs.readFileSync(DATA_FILE, 'utf8'));
        // Convert both IDs to strings for comparison to handle mixed types
        const originalLength = mapData.poles.length;
        mapData.poles = mapData.poles.filter(pole => String(pole.id) !== String(poleId));
        if (mapData.poles.length < originalLength) {
          deleted = true;
          fs.writeFileSync(DATA_FILE, JSON.stringify(mapData, null, 2));
          console.log('Updated backup file after pole deletion');
        }
      }
    } catch (fileError) {
      console.error('Error updating backup file after deletion:', fileError);
    }
    
    if (deleted) {
      res.json({ success: true, message: 'Pole deleted successfully' });
    } else {
      res.status(404).json({ success: false, message: 'Pole not found' });
    }
  } catch (error) {
    console.error('Error deleting pole:', error);
    res.status(500).json({ success: false, message: 'Error deleting pole', error: error.message });
  }
});

// API endpoint for deleting a turbine
app.delete('/api/turbines/:id', async (req, res) => {
  try {
    const turbineId = req.params.id;
    console.log('Attempting to delete turbine with ID:', turbineId, 'Type:', typeof turbineId);
    
    let deleted = false;
    
    // Delete from PostgreSQL if available
    if (pool) {
      try {
        const result = await pool.query('DELETE FROM turbines WHERE id = $1 RETURNING *', [turbineId]);
        if (result.rowCount > 0) {
          deleted = true;
          console.log('Turbine deleted from database');
        }
      } catch (dbError) {
        console.error('Database error when deleting turbine:', dbError);
      }
    }
    
    // Also update file-based backup if it exists
    try {
      if (fs.existsSync(DATA_FILE)) {
        const mapData = JSON.parse(fs.readFileSync(DATA_FILE, 'utf8'));
        // Convert both IDs to strings for comparison to handle mixed types
        const originalLength = mapData.turbines.length;
        mapData.turbines = mapData.turbines.filter(turbine => String(turbine.id) !== String(turbineId));
        if (mapData.turbines.length < originalLength) {
          deleted = true;
          fs.writeFileSync(DATA_FILE, JSON.stringify(mapData, null, 2));
          console.log('Updated backup file after turbine deletion');
        }
      }
    } catch (fileError) {
      console.error('Error updating backup file after deletion:', fileError);
    }
    
    if (deleted) {
      res.json({ success: true, message: 'Turbine deleted successfully' });
    } else {
      res.status(404).json({ success: false, message: 'Turbine not found' });
    }
  } catch (error) {
    console.error('Error deleting turbine:', error);
    res.status(500).json({ success: false, message: 'Error deleting turbine', error: error.message });
  }
});

// Debug route to check server status
app.get('/api/status', async (req, res) => {
  try {
    // Test database connection only if pool exists
    let dbConnected = false;
    let dbTime = null;
    
    if (pool) {
      try {
        const dbResult = await pool.query('SELECT NOW() as time');
        dbConnected = !!dbResult.rows[0];
        dbTime = dbResult.rows[0]?.time;
      } catch (dbError) {
        console.error('Database status check failed:', dbError);
        dbConnected = false;
      }
    }
    
    res.json({
      status: 'ok',
      timestamp: new Date().toISOString(),
      directories: {
        pdfPylon: fs.existsSync(pdfPylonDir),
        pdfTurbine: fs.existsSync(pdfTurbineDir),
        thumbnail: fs.existsSync(thumbnailDir),
        data: fs.existsSync(DATA_DIR),
        dataFile: fs.existsSync(DATA_FILE)
      },
      paths: {
        pdfPylon: pdfPylonDir,
        pdfTurbine: pdfTurbineDir,
        thumbnail: thumbnailDir,
        data: DATA_DIR,
        dataFile: DATA_FILE
      },
      environment: {
        nodeEnv: process.env.NODE_ENV,
        port: PORT,
        isProduction
      },      database: {
        connected: dbConnected,
        time: dbTime
      }
    });
  } catch (error) {
    console.error('Error in status endpoint:', error);
    res.status(500).json({
      status: 'error',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Database diagnostics endpoint
app.get('/api/db-diagnostics', async (req, res) => {
  try {
    console.log('Running database diagnostics...');
    
    // Check environment variables
    const envVars = {
      DATABASE_URL: process.env.DATABASE_URL ? 'Provided (value hidden)' : 'Missing',
      DATABASE_PUBLIC_URL: process.env.DATABASE_PUBLIC_URL ? 'Provided (value hidden)' : 'Missing',
      PGHOST: process.env.PGHOST || 'Not set',
      PGPORT: process.env.PGPORT || 'Not set',
      PGUSER: process.env.PGUSER || 'Not set',
      PGDATABASE: process.env.PGDATABASE || 'Not set',
      NODE_ENV: process.env.NODE_ENV || 'Not set'
    };
    
    // Show which URL is being used (same logic as main connection)
    let activeUrl = null;
    let urlSource = 'none';
    
    // Try DATABASE_PUBLIC_URL first (preferred for Railway)
    if (process.env.DATABASE_PUBLIC_URL) {
      activeUrl = process.env.DATABASE_PUBLIC_URL;
      urlSource = 'DATABASE_PUBLIC_URL (public proxy)';
    } 
    // Try DATABASE_URL second
    else if (process.env.DATABASE_URL) {
      activeUrl = process.env.DATABASE_URL;
      urlSource = 'DATABASE_URL (internal)';
    } 
    // Try POSTGRES_URL third
    else if (process.env.POSTGRES_URL) {
      activeUrl = process.env.POSTGRES_URL;
      urlSource = 'POSTGRES_URL';
    }
    // Try to construct from individual PG variables fourth
    else if (process.env.PGHOST && process.env.PGDATABASE && process.env.PGUSER && process.env.PGPASSWORD) {
      const port = process.env.PGPORT || '5432';
      activeUrl = `postgresql://${process.env.PGUSER}:${process.env.PGPASSWORD}@${process.env.PGHOST}:${port}/${process.env.PGDATABASE}`;
      urlSource = 'Constructed from PG* variables';
    }
    
    // Parse connection string if available
    let connectionInfo = {};
    if (activeUrl) {
      try {
        const url = new URL(activeUrl);
        connectionInfo = {
          host: url.hostname,
          port: url.port,
          username: url.username,
          password: 'Hidden',
          database: url.pathname.substring(1),
          source: urlSource
        };
      } catch (e) {
        connectionInfo = { error: 'Invalid database URL format', source: urlSource };
      }
    } else {
      connectionInfo = { error: 'No database URL available' };
    }
    
    // Try to connect to the database
    let connectionTest = {};
    try {
      console.log('Testing direct database connection...');
      const client = await pool.connect();
      const dbVersionResult = await client.query('SELECT version()');
      const dbVersion = dbVersionResult.rows[0]?.version;
      const tablesResult = await client.query(`
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public'
      `);
      const tables = tablesResult.rows.map(row => row.table_name);
      client.release();
      
      connectionTest = {
        success: true,
        version: dbVersion,
        tables: tables
      };
    } catch (error) {
      connectionTest = {
        success: false,
        error: error.message,
        stack: error.stack
      };
    }
    
    // Return all diagnostic info
    res.json({
      timestamp: new Date().toISOString(),
      environment: envVars,
      connectionString: connectionInfo,
      connectionTest: connectionTest,
      fileSystem: {
        dataDir: fs.existsSync(DATA_DIR),
        dataFile: fs.existsSync(DATA_FILE) ? 
          { exists: true, size: fs.statSync(DATA_FILE).size } : 
          { exists: false }
      }
    });
  } catch (error) {
    console.error('Error in database diagnostics:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      stack: error.stack
    });
  }
});

// Process termination handlers
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully');
  process.exit(0);
});

// Handle uncaught exceptions to prevent crashing
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
});

// Serve the index.html file for any route
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'index.html'));
});

// Initialize the database and start the server
async function startServer() {
  let dbConnected = false;
  
  // Try to connect to the database if pool exists
  if (pool) {
    try {
      console.log('Testing PostgreSQL connection...');
      const client = await pool.connect();
      console.log('Database connection successful');
      client.release();
      dbConnected = true;
      
      // Initialize database tables
      try {
        await initDatabase();
      } catch (dbInitError) {
        console.error('Error initializing database tables, continuing anyway:', dbInitError.message);
      }
    } catch (dbError) {
      console.error('Failed to connect to PostgreSQL database, continuing in file-only mode:', dbError.message);
    }
  } else {
    console.log('PostgreSQL disabled - using file-only mode');
  }
  
  // Start the server regardless of database connection status
  app.listen(PORT, () => {
    console.log(`Server running on port ${PORT}`);
    console.log(`Environment: ${isProduction ? 'Production' : 'Development'}`);
    console.log(`Database connected: ${dbConnected ? 'Yes' : 'No, using file-based storage'}`);
    console.log(`Data directory: ${DATA_DIR}`);
    console.log(`PDF Pylon directory: ${pdfPylonDir}`);
    console.log(`PDF Turbine directory: ${pdfTurbineDir}`);
    console.log(`Thumbnail directory: ${thumbnailDir}`);
  });
}

// Start the server
startServer();