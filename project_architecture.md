# Project Architecture: Interactive Power Line Plotting App

**Client:** BrandSA
**Technology Provider:** ADS

## 1. Project Overview

Develop an interactive, client-side web-based application for BrandSA to plot and manage power line poles related to a wind farm. The application will allow users to add poles with geographical coordinates, associate PDF reports and image thumbnails with each pole, and save/load all data to a local JSON file.

## 2. Core Features

*   **Interactive Mapping:**
    *   Display an interactive map using Leaflet.js.
    *   Allow users to add new poles by specifying latitude and longitude, or potentially by clicking on the map.
    *   Display poles as markers on the map.
*   **Pole Data Management:**
    *   For each pole, store:
        *   A unique identifier (`id`).
        *   Pole number (`poleNumber`).
        *   Geographical coordinates (`latitude`, `longitude`).
        *   Associated PDF report data (`reportPDFData` as Base64 string).
        *   Associated image thumbnail data (`thumbnailImageData` as Base64 string).
*   **Reporting and Thumbnails:**
    *   Users can upload a PDF file as a report for each pole.
    *   Users can upload an image file (e.g., JPG, PNG) as a thumbnail for each report.
*   **User Interaction & Display:**
    *   Clicking a pole marker on the map will trigger a popup.
    *   The popup will display:
        *   The `poleNumber`.
        *   The associated image thumbnail.
        *   A button/link to "View Full PDF Report".
    *   Clicking the "View Full PDF Report" button will open/display the associated PDF.
*   **Data Persistence:**
    *   All application data (pole information, Base64 encoded PDF reports, and Base64 encoded image thumbnails) will be stored in a JavaScript array of objects client-side.
    *   **Save Data:** Functionality to serialize the entire dataset into a JSON string and prompt the user to download it as a `.json` file.
    *   **Load Data:** Functionality to allow users to select a previously saved `.json` file, parse it, and repopulate the application with the data.
*   **Branding:**
    *   The application will display project identification such as "BrandSA Wind Farm Power Lines".
    *   The application will display a credit line: "Powered by ADS".

## 3. Technology Stack

*   **Frontend:** Plain HTML, CSS, JavaScript.
*   **Mapping Library:** Leaflet.js (with a suitable tile layer like OpenStreetMap).
*   **Data Format for Save/Load:** JSON.

## 4. Architectural Diagram (Conceptual)

```mermaid
graph TD
    User[User] --> BrowserInterface[Browser: index.html, style.css]

    BrowserInterface --> MapContainer[Map Container (Leaflet)]
    BrowserInterface --> UIControls[UI Controls: Pole Inputs, PDF Upload, Thumbnail Upload, Save/Load Buttons]
    BrowserInterface --> AppJS[app.js: Main Application Logic]

    AppJS --> LeafletJS[Leaflet.js Library]
    LeafletJS --> MapContainer

    AppJS --> PoleDataStore[In-Memory Pole Data (Array of Objects)]
    PoleDataStore --> PoleObject[Pole Object: id, poleNumber, lat, lon, reportPDFData (Base64), thumbnailImageData (Base64)]

    AppJS --> PoleFunctions[Pole Management Functions: Add, Display on Map]
    AppJS --> ReportThumbnailFunctions[Report & Thumbnail Handling: Upload, Base64 Convert, Store]
    AppJS --> FileSaverLoader[Data Save/Load to JSON File Logic]

    UIControls -- User Actions --> AppJS
    MapContainer -- Map Interactions (e.g., Pole Click) --> AppJS

    PoleFunctions --> PoleDataStore
    PoleFunctions --> LeafletJS
    ReportThumbnailFunctions --> PoleDataStore

    FileSaverLoader -- Save (JSON with Base64 PDFs & Thumbnails) --> UserDownloadsFile[User Downloads .json File]
    UserUploadsFile[User Uploads .json File] -- Load --> FileSaverLoader
    FileSaverLoader --> PoleDataStore

    subgraph ClientSideApplication
        BrowserInterface
        MapContainer
        UIControls
        AppJS
    end
```

## 5. Key Workflow Considerations

1.  **Initialization:** User opens `index.html`. Map and UI elements are initialized.
2.  **Data Loading (Optional):** User clicks "Load Data", selects their JSON file. Application parses it, populates `PoleDataStore`, and renders poles on the map.
3.  **Adding a Pole:**
    *   User inputs pole details (number, lat, lon).
    *   User uploads a PDF report.
    *   User uploads an image thumbnail.
    *   Application converts PDF and image to Base64, creates a new pole object, adds it to `PoleDataStore`, and places a marker on the map.
4.  **Interacting with a Pole:**
    *   User clicks a pole marker.
    *   Popup appears showing pole number and thumbnail.
    *   User clicks "View Full PDF Report" to open/view the PDF.
5.  **Saving Data:** User clicks "Save Data". Application serializes `PoleDataStore` to JSON and initiates a file download for the user.

## 6. Potential Considerations

*   **File Size:** Storing PDF and image data as Base64 strings directly in the JSON save file can lead to large file sizes, potentially impacting browser performance during save/load operations for very large datasets. This is a trade-off for keeping all data self-contained.
*   **Error Handling:** Robust error handling for file operations (loading invalid JSON, issues with file reading) should be implemented.
*   **User Experience:** Clear feedback to the user during file operations (e.g., "Loading data...", "Data saved.") is important.