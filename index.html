<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BrandSA Wind Farm Power Lines - Powered by ADS</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
          integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY="
          crossorigin=""/>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <!-- Login Page -->
    <div id="loginPage" class="login-container">
        <div class="login-card">            <div class="login-header">                <div class="company-logos">                    <div class="ads-logo">
                        <img src="ADS Logo.png" alt="ADS Logo" class="company-logo-img">
                        <span>Aerial Dynamic Solutions</span>
                        <small>Professional Aviation</small>
                    </div><div class="brandsa-logo">
                        <div class="brandsa-triangle">▲</div>
                        <span>BrandSA</span>
                        <small>energy infrastructure</small>
                    </div>
                    <div class="redrocket-logo">
                        <i class="fas fa-rocket"></i>
                        <span>Red Rocket Energy</span>
                        <small>Energy Solutions</small>
                    </div>
                </div>
                <h1>Power Grid Management System</h1>
                <p>Industrial Access Portal</p>
            </div>
            <form id="loginForm" class="login-form">
                <div class="input-group">
                    <i class="fas fa-user"></i>
                    <input type="text" id="username" placeholder="Username (BrandSA / RedRocket / ADS)" required>
                </div>                <div class="input-group">
                    <i class="fas fa-lock"></i>
                    <input type="password" id="password" placeholder="Password" required>
                </div>
                <button type="submit" id="loginBtn" class="login-btn">
                    <i class="fas fa-sign-in-alt"></i> Secure Login
                </button>
            </form>
            <div class="login-footer">
                <p><i class="fas fa-shield-alt"></i> Secure access to power grid infrastructure data</p>
                <small>© 2025 Aerial Dynamic Solutions - Professional Drone Services</small>
            </div>
        </div>
    </div>

    <!-- Main Application -->
    <div id="mainApp" class="main-app" style="display: none;">
        <header class="app-header">
            <div class="header-left">                <div class="company-branding">                    <div class="ads-brand">
                        <img src="ADS Logo.png" alt="ADS Logo" class="company-logo-img-small">
                        <span>ADS</span>
                    </div>
                    <div class="separator">|</div>                    <div class="client-brand">
                        <i class="fas fa-wind"></i>
                        <span id="clientBrandName">BrandSA Wind Farm Power Lines</span>
                    </div>
                </div>
            </div>            <div class="header-right">
                <div class="search-container">
                    <div class="search-input-group">
                        <i class="fas fa-search"></i>
                        <input type="text" id="assetSearchInput" placeholder="Search by Pole Number or Turbine Name..." />
                        <button id="searchButton" class="search-btn">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                    <div id="searchResults" class="search-results" style="display: none;"></div>
                </div>
                <div class="user-info">
                    <i class="fas fa-user"></i>
                    <span id="currentUserName">User</span>
                    <span id="currentUserRole" class="user-role">Client</span>
                </div>
                <button id="logoutBtn" class="logout-btn" style="display: none;">
                    <i class="fas fa-sign-out-alt"></i> Logout
                </button>
            </div>
        </header>        <main>
            <div id="map-container">
                <div id="map"></div>
            </div>

            <div id="controls-panel">
                <!-- Asset Type Selector -->
                <div class="asset-type-selector">
                    <h2><i class="fas fa-plus"></i> Add New Asset</h2>
                    <div class="asset-tabs">
                        <button id="poleTabBtn" class="asset-tab active" data-type="pole">
                            <i class="fas fa-tower-broadcast"></i>
                            <span>Power Pole</span>
                        </button>
                        <button id="turbineTabBtn" class="asset-tab" data-type="turbine">
                            <i class="fas fa-fan"></i>
                            <span>Wind Turbine</span>
                        </button>
                    </div>
                </div>

                <!-- Pole Form -->
                <div id="poleForm" class="asset-form active">
                    <h3><i class="fas fa-tower-broadcast"></i> Add New Pole</h3>
                    <div class="form-group">
                        <label for="poleNumber"><i class="fas fa-hashtag"></i> Pole Number:</label>
                        <input type="text" id="poleNumber" name="poleNumber" placeholder="e.g., BV_C1-01">
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="latitude"><i class="fas fa-map-marker-alt"></i> Latitude:</label>
                            <input type="text" id="latitude" name="latitude" placeholder="-32.963025">
                        </div>
                        <div class="form-group">
                            <label for="longitude"><i class="fas fa-map-marker-alt"></i> Longitude:</label>
                            <input type="text" id="longitude" name="longitude" placeholder="20.487796">
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="pdfReport"><i class="fas fa-file-pdf"></i> PDF Report:</label>
                        <input type="file" id="pdfReport" name="pdfReport" accept=".pdf">
                    </div>
                    <div class="form-group">
                        <label for="thumbnailImage"><i class="fas fa-image"></i> Thumbnail Image:</label>
                        <input type="file" id="thumbnailImage" name="thumbnailImage" accept="image/*">
                    </div>
                    <button id="addPoleButton" class="add-btn">
                        <i class="fas fa-plus"></i> Add Pole
                    </button>
                </div>

                <!-- Turbine Form -->
                <div id="turbineForm" class="asset-form">
                    <h3><i class="fas fa-fan"></i> Add New Turbine</h3>
                    <div class="form-group">
                        <label for="turbineName"><i class="fas fa-tag"></i> Turbine Name/ID:</label>
                        <input type="text" id="turbineName" name="turbineName" placeholder="e.g., WT-001">
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="turbineLatitude"><i class="fas fa-map-marker-alt"></i> Latitude:</label>
                            <input type="text" id="turbineLatitude" name="turbineLatitude" placeholder="-32.963025">
                        </div>
                        <div class="form-group">
                            <label for="turbineLongitude"><i class="fas fa-map-marker-alt"></i> Longitude:</label>
                            <input type="text" id="turbineLongitude" name="turbineLongitude" placeholder="20.487796">
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="turbinePdfReport"><i class="fas fa-file-pdf"></i> PDF Report:</label>
                        <input type="file" id="turbinePdfReport" name="turbinePdfReport" accept=".pdf">
                    </div>
                    <div class="form-group">
                        <label for="turbineThumbnailImage"><i class="fas fa-image"></i> Thumbnail Image:</label>
                        <input type="file" id="turbineThumbnailImage" name="turbineThumbnailImage" accept="image/*">
                    </div>
                    <button id="addTurbineButton" class="add-btn">
                        <i class="fas fa-plus"></i> Add Turbine
                    </button>
                </div>

                <hr>                <div class="data-management">
                    <h3><i class="fas fa-database"></i> Data Management</h3>
                    <div class="data-buttons">
                        <button id="saveDataButton" class="data-btn">
                            <i class="fas fa-download"></i> Export Data
                        </button>
                        <button id="loadDataButton" class="data-btn admin-only">
                            <i class="fas fa-upload"></i> Import Data
                        </button>
                        <button id="refreshDataButton" class="data-btn">
                            <i class="fas fa-sync-alt"></i> Refresh Data
                        </button>
                    </div>
                    <input type="file" id="loadFileInput" accept=".json" style="display: none;">
                </div>

                <!-- Admin Only Section -->
                <div id="adminControls" class="admin-controls" style="display: none;">
                    <hr>
                    <h3><i class="fas fa-shield-alt"></i> Admin Controls</h3>
                    <div class="admin-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <span>Administrative functions - Use with caution</span>
                    </div>
                </div>
            </div>
        </main>

        <div id="notification-area"></div>

        <footer>
            <div class="footer-content">                <div class="footer-left">
                    <p><strong id="footerBrandName">BrandSA Wind Farm Power Lines</strong> - Professional Mapping Application</p>
                </div>
                <div class="footer-right">
                    <p>Powered by <strong>Aerial Dynamic Solutions</strong> - Professional Drone Services</p>
                    <small>© 2025 ADS. All rights reserved.</small>
                </div>
            </div>
        </footer>
    </div>

    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
            integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo="
            crossorigin=""></script>
    <script src="app.js"></script>

    <!-- PDF Modal Structure -->
    <div id="pdfModal" class="modal">
        <div class="modal-content">
            <span class="modal-close-button">&times;</span>
            <iframe id="pdfModalIframe" src="" frameborder="0"></iframe>
        </div>
    </div>

    <!-- Add progress bar container to the body -->
    <div id="uploadProgressContainer" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5); z-index: 1000;">
      <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background-color: white; padding: 20px; border-radius: 5px; box-shadow: 0 0 10px rgba(0,0,0,0.3); width: 300px; text-align: center;">
        <h3 id="uploadProgressTitle">Uploading...</h3>
        <div style="height: 20px; background-color: #f0f0f0; border-radius: 10px; margin: 10px 0; overflow: hidden;">
          <div id="uploadProgressBar" style="height: 100%; width: 0%; background-color: #4CAF50; transition: width 0.3s;"></div>
        </div>
        <div id="uploadProgressText">0%</div>
      </div>
    </div>
</body>
</html>
